import { Metadata } from 'next'
import { getTranslations } from 'next-intl/server'
import Link from 'next/link'
import { ArrowRight, Check, Users, Award, Globe } from 'lucide-react'
import { Button } from '@/components/ui/button'

export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string }>
}): Promise<Metadata> {
  const { locale: resolvedLocale } = await params
  const t = await getTranslations('hero')
  
  const metadataByLocale = {
    en: {
      title: 'Professional Wallpaper Adhesive Powder Manufacturer | Kahn Materials',
      description: '15+ years experience manufacturing high-quality wallpaper adhesive powder. ISO9001 certified, global export. Get free samples and competitive quotes.',
      keywords: 'wallpaper adhesive powder, wallpaper glue powder, starch adhesive, PVA adhesive, wallpaper glue manufacturer'
    },
    zh: {
      title: '专业墙纸胶粉制造商 - 杭州卡恩新型建材有限公司',
      description: '15年专注墙纸胶粉研发生产，ISO9001认证，全球出口。免费样品，快速报价，500+国际客户信赖。',
      keywords: '墙纸胶粉, 改性淀粉胶粉, PVA胶粉, 墙纸胶厂家, 墙纸胶粉出口'
    },
    ru: {
      title: 'Профессиональный производитель клеевой смеси для обоев | Кан Материалы',
      description: '15+ лет опыта производства высококачественной клеевой смеси для обоев. Сертификат ISO9001, глобальный экспорт. Бесплатные образцы.',
      keywords: 'клеевая смесь для обоев, клей для обоев порошок, клейстер для обоев, производитель клея для обоев'
    }
  }

  return {
    title: metadataByLocale[resolvedLocale as keyof typeof metadataByLocale].title,
    description: metadataByLocale[resolvedLocale as keyof typeof metadataByLocale].description,
    keywords: metadataByLocale[resolvedLocale as keyof typeof metadataByLocale].keywords,
    openGraph: {
      title: metadataByLocale[resolvedLocale as keyof typeof metadataByLocale].title,
      description: metadataByLocale[resolvedLocale as keyof typeof metadataByLocale].description,
      images: ['/api/placeholder/1200/630'],
      type: 'website',
      locale: resolvedLocale,
    },
    twitter: {
      card: 'summary_large_image',
      title: metadataByLocale[resolvedLocale as keyof typeof metadataByLocale].title,
      description: metadataByLocale[resolvedLocale as keyof typeof metadataByLocale].description,
      images: ['/api/placeholder/1200/630'],
    },
    alternates: {
      canonical: `https://kahn-materials.com/${resolvedLocale}`,
      languages: {
        'en': 'https://kahn-materials.com/en',
        'zh': 'https://kahn-materials.com/zh',
        'ru': 'https://kahn-materials.com/ru',
      },
    },
  }
}

export default async function HomePage({
  params
}: {
  params: Promise<{ locale: string }>
}) {
  const { locale: resolvedLocale } = await params
  const t = await getTranslations('hero')
  const products = await getTranslations('products')

  const features = [
    {
      icon: Award,
      title: 'ISO9001 Certified',
      description: 'Quality management system certified'
    },
    {
      icon: Globe,
      title: 'Global Export',
      description: 'Export to 50+ countries worldwide'
    },
    {
      icon: Users,
      title: '500+ Clients',
      description: 'Trusted by international brands'
    }
  ]

  const productFeatures = products.raw('features') as string[]

  // Add JSON-LD structured data
  const jsonLd = {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: 'Hangzhou Kahn New Building Materials Co., Ltd.',
    url: 'https://kahn-materials.com',
    logo: 'https://kahn-materials.com/logo.png',
    description: 'Professional wallpaper adhesive powder manufacturer with 15+ years experience',
    contactPoint: {
      '@type': 'ContactPoint',
      telephone: '+86-571-8278-8888',
      contactType: 'sales',
      areaServed: 'Global',
      availableLanguage: ['English', 'Chinese', 'Russian']
    },
    sameAs: [
      'https://linkedin.com/company/kahn-materials',
      'https://facebook.com/kahn-materials',
      'https://twitter.com/kahn_materials'
    ]
  }

  return (
    <div className="min-h-screen">
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      
      {/* Hero Section with enhanced SEO */}
      <section className="relative bg-gradient-to-br from-kahn-blue to-kahn-blue-dark text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="text-center lg:text-left">
              <h1 className="text-4xl lg:text-6xl font-bold mb-6 leading-tight">
                {t('title')}
              </h1>
              <p className="text-xl lg:text-2xl mb-4 text-blue-100">
                {t('subtitle')}
              </p>
              <p className="text-lg mb-8 text-blue-200">
                {t('trust')}
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                <Link href="/contact">
                  <Button size="lg" className="bg-white text-kahn-blue hover:bg-gray-100">
                    {t('cta')}
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Button>
                </Link>
                <Link href="/products">
                  <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-kahn-blue">
                    View Products
                  </Button>
                </Link>
              </div>
            </div>
            
            <div className="relative">
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8">
                <div className="relative">
                  <img 
                    src="/api/placeholder/600/400" 
                    alt="Professional wallpaper adhesive powder manufacturing facility"
                    className="rounded-lg shadow-2xl w-full h-auto"
                    width="600"
                    height="400"
                    loading="eager"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-lg" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="text-center p-6 rounded-lg hover:shadow-lg transition-shadow">
                <div className="w-16 h-16 bg-kahn-blue/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <feature.icon className="w-8 h-8 text-kahn-blue" />
                </div>
                <h3 className="text-xl font-semibold mb-2">{feature.title}</h3>
                <p className="text-gray-600">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Products Preview */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              {products('title')}
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              {products('subtitle')}
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 mb-12">
            {['starch', 'pva', 'pregel'].map((type) => (
              <article key={type} className="bg-white rounded-xl shadow-md p-6 hover:shadow-lg transition-shadow">
                <div className="w-full h-48 bg-gray-200 rounded-lg mb-4 overflow-hidden">
                  <img 
                    src={`/api/placeholder/400/300`} 
                    alt={`${products(`categories.${type}`)} wallpaper adhesive powder`}
                    className="w-full h-full object-cover"
                    width="400"
                    height="300"
                    loading="lazy"
                  />
                </div>
                <h3 className="text-xl font-semibold mb-2">
                  {products(`categories.${type}`)}
                </h3>
                <ul className="space-y-2 mb-4">
                  {productFeatures.map((feature, index) => (
                    <li key={index} className="flex items-center text-sm text-gray-600">
                      <Check className="w-4 h-4 text-green-500 mr-2" />
                      {feature}
                    </li>
                  ))}
                </ul>
                <Link href="/products">
                  <Button className="w-full">Learn More</Button>
                </Link>
              </article>
            ))}
          </div>

          <div className="text-center">
            <Link href="/products">
              <Button variant="outline" size="lg" className="group">
                View All Products
                <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-kahn-blue text-white">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold mb-4">
            Ready to Start Your Project?
          </h2>
          <p className="text-xl mb-8 text-blue-100">
            Get a free sample and competitive quote within 24 hours
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/contact">
              <Button size="lg" className="bg-white text-kahn-blue hover:bg-gray-100">
                Request Free Sample
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
            <Link href="/contact">
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-kahn-blue">
                Contact Sales Team
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  )
}