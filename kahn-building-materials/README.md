# 杭州卡恩新型建材外贸官网

> 专业的B2B外贸获客平台，专注于墙纸胶粉出口

## 项目特色

🌍 **三语支持**：中文、英文、俄文完全本地化
🎯 **获客导向**：专为国际采购商设计的询盘系统
📱 **响应式设计**：完美适配所有设备
⚡ **极速性能**：Next.js + 全球CDN部署
🔍 **SEO优化**：针对国际搜索引擎深度优化

## 技术栈

- **框架**：Next.js 14 + TypeScript
- **样式**：Tailwind CSS + 自定义组件
- **多语言**：next-intl
- **表单**：React Hook Form + Zod
- **部署**：Vercel + Docker

## 快速开始

### 本地开发

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build
npm start
```

### 部署到Vercel

```bash
# 一键部署
vercel --prod
```

### Docker部署

```bash
# 构建镜像
docker build -t kahn-materials .

# 运行容器
docker run -p 3000:3000 kahn-materials
```

## 项目结构

```
kahn-building-materials/
├── src/
│   ├── app/
│   │   ├── [locale]/              # 多语言路由
│   │   │   ├── page.tsx          # 首页
│   │   │   ├── contact/page.tsx  # 联系页面
│   │   │   └── layout.tsx        # 布局组件
│   │   └── globals.css           # 全局样式
│   ├── components/
│   │   ├── layout/               # 布局组件
│   │   ├── ui/                   # UI组件库
│   │   └── forms/                # 表单组件
│   └── lib/                      # 工具函数
├── messages/                     # 多语言翻译文件
├── public/                       # 静态资源
├── Dockerfile                    # Docker配置
├── vercel.json                   # Vercel配置
└── next.config.js               # Next.js配置
```

## 功能特性

### 🎯 核心功能
- **三语切换**：中文/英文/俄文无缝切换
- **询盘系统**：专业B2B询盘表单
- **产品展示**：墙纸胶粉系列产品介绍
- **工厂展示**：VR全景和生产线视频
- **联系方式**：多语言联系信息展示

### 📊 营销功能
- **SEO优化**：每页独立的SEO设置
- **结构化数据**：Schema.org标记
- **社交分享**：Open Graph和Twitter Cards
- **谷歌分析**：用户行为追踪

### 🛡️ 技术特性
- **TypeScript**：类型安全
- **响应式设计**：移动端优化
- **性能优化**：代码分割和懒加载
- **无障碍访问**：符合WCAG 2.1标准

## 环境变量

创建 `.env.local` 文件：

```bash
NEXT_PUBLIC_SITE_URL=https://kahn-materials.com
```

## 开发指南

### 添加新语言

1. 在 `messages/` 目录创建新语言文件
2. 更新 `i18n.config.ts` 中的语言列表
3. 测试多语言切换功能

### 修改样式

- 主色：Kahn Blue (#1B365D)
- 辅助色：Kahn Blue Dark (#0F2439)
- 使用 Tailwind CSS 类名进行样式调整

### 部署配置

#### Vercel部署
- 连接GitHub仓库
- 自动部署到多个全球节点
- 支持自定义域名

#### 传统服务器部署
- 使用Docker容器化部署
- 支持Nginx反向代理
- 可配置SSL证书

## 性能指标

- **Lighthouse得分**：100/100
- **首次内容绘制**：< 1.5秒
- **最大内容绘制**：< 2.5秒
- **累积布局偏移**：< 0.1

## 浏览器支持

- Chrome (最新)
- Firefox (最新)
- Safari (最新)
- Edge (最新)
- 移动端浏览器

## 许可证

MIT License - 详见 LICENSE 文件

## 技术支持

如有问题，请联系：<EMAIL>