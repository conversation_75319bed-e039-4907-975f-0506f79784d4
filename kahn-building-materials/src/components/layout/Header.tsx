'use client'

import Link from 'next/link'
import { useLocale, useTranslations } from 'next-intl'
import { Globe, Menu } from 'lucide-react'
import { useState } from 'react'
import { Button } from '@/components/ui/button'

export function Header() {
  const locale = useLocale()
  const t = useTranslations('nav')
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  const languages = [
    { code: 'en', name: 'English', flag: '🇺🇸' },
    { code: 'zh', name: '中文', flag: '🇨🇳' },
    { code: 'ru', name: 'Русский', flag: '🇷🇺' },
  ]

  const navigation = [
    { href: '/', label: t('home') },
    { href: '/products', label: t('products') },
    { href: '/factory', label: t('factory') },
    { href: '/about', label: t('about') },
    { href: '/contact', label: t('contact') },
  ]

  return (
    <header className="bg-white shadow-sm sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2">
            <div className="w-10 h-10 bg-kahn-blue rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-lg">K</span>
            </div>
            <div className="hidden md:block">
              <h1 className="text-xl font-bold text-gray-900">Kahn Materials</h1>
              <p className="text-xs text-gray-500">Wallpaper Adhesive Powder</p>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            {navigation.map((item) => (
              <Link
                key={item.href}
                href={`/${locale}${item.href === '/' ? '' : item.href}`}
                className="text-gray-700 hover:text-kahn-blue transition-colors font-medium"
              >
                {item.label}
              </Link>
            ))}
          </nav>

          {/* Language Selector & CTA */}
          <div className="flex items-center space-x-4">
            <div className="relative group">
              <button className="flex items-center space-x-1 px-3 py-2 text-gray-700 hover:text-kahn-blue transition-colors">
                <Globe size={18} />
                <span className="text-sm font-medium">{locale.toUpperCase()}</span>
              </button>
              <div className="absolute right-0 mt-2 w-40 bg-white rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all">
                {languages.map((lang) => (
                  <Link
                    key={lang.code}
                    href={`/${lang.code}`}
                    className="flex items-center space-x-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg"
                  >
                    <span>{lang.flag}</span>
                    <span>{lang.name}</span>
                  </Link>
                ))}
              </div>
            </div>
            <Link href={`/${locale}/contact`}>
              <Button className="hidden md:inline-flex">{t('inquiry')}</Button>
            </Link>
          </div>

          {/* Mobile menu button */}
          <button
            className="md:hidden p-2"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            <Menu size={24} />
          </button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden py-4 border-t">
            <nav className="flex flex-col space-y-2">
              {navigation.map((item) => (
                <Link
                  key={item.href}
                  href={`/${locale}${item.href === '/' ? '' : item.href}`}
                  className="px-4 py-2 text-gray-700 hover:text-kahn-blue hover:bg-gray-50 rounded-lg"
                  onClick={() => setIsMenuOpen(false)}
                >
                  {item.label}
                </Link>
              ))}
            </nav>
            <div className="mt-4 px-4">
              <Link href={`/${locale}/contact`}>
                <Button className="w-full">{t('inquiry')}</Button>
              </Link>
            </div>
          </div>
        )}
      </div>
    </header>
  )
}