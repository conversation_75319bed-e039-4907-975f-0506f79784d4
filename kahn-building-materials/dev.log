
> kahn-building-materials@0.1.0 dev
> next dev --turbopack

   ▲ Next.js 15.4.6 (Turbopack)
   - Local:        http://localhost:3000
   - Network:      http://************:3000

 ✓ Starting...
 ✓ Compiled middleware in 97ms
 ✓ Ready in 930ms
 ○ Compiling /[locale] ...
Error: Cannot apply unknown utility class `border-border`. Are you using CSS modules or similar and missing `@reference`? https://tailwindcss.com/docs/functions-and-directives#reference-directive
    [2m[at onInvalidCandidate (/Users/<USER>/Documents/聂磊卡恩/kahn-building-materials/node_modules/tailwindcss/dist/lib.js:18:1312)][0m
    [2m[at ge (/Users/<USER>/Documents/聂磊卡恩/kahn-building-materials/node_modules/tailwindcss/dist/lib.js:13:29803)][0m
    [2m[at /Users/<USER>/Documents/聂磊卡恩/kahn-building-materials/node_modules/tailwindcss/dist/lib.js:18:373][0m
    [2m[at I (/Users/<USER>/Documents/聂磊卡恩/kahn-building-materials/node_modules/tailwindcss/dist/lib.js:3:1656)][0m
    [2m[at je (/Users/<USER>/Documents/聂磊卡恩/kahn-building-materials/node_modules/tailwindcss/dist/lib.js:18:172)][0m
    [2m[at bi (/Users/<USER>/Documents/聂磊卡恩/kahn-building-materials/node_modules/tailwindcss/dist/lib.js:35:780)][0m
    [2m[at process.processTicksAndRejections (node:internal/process/task_queues:105:5)][0m
    [2m[at async yi (/Users/<USER>/Documents/聂磊卡恩/kahn-building-materials/node_modules/tailwindcss/dist/lib.js:35:1123)][0m
    [2m[at async _r (/Users/<USER>/Documents/聂磊卡恩/kahn-building-materials/node_modules/@tailwindcss/node/dist/index.js:10:3384)][0m
    [2m[at async p (/Users/<USER>/Documents/聂磊卡恩/kahn-building-materials/node_modules/@tailwindcss/postcss/dist/index.js:10:4019)][0m
 ✓ Compiled /[locale] in 3.6s
 ⨯ ./src/components/layout/Header.tsx:32:12
Parsing ecmascript source code failed
[0m [90m 30 |[39m       [33m<[39m[33mdiv[39m className[33m=[39m[32m"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"[39m[33m>[39m
 [90m 31 |[39m         [33m<[39m[33mdiv[39m className[33m=[39m[32m"flex justify-between items-center h-16"[39m[33m>[39m
[31m[1m>[22m[39m[90m 32 |[39m           [33m<[39m[33m![39m[33m--[39m [33mLogo[39m [33m--[39m[33m>[39m
 [90m    |[39m            [31m[1m^[22m[39m
 [90m 33 |[39m           [33m<[39m[33mLink[39m href[33m=[39m[32m"/"[39m className[33m=[39m[32m"flex items-center space-x-2"[39m[33m>[39m
 [90m 34 |[39m             [33m<[39m[33mdiv[39m className[33m=[39m[32m"w-10 h-10 bg-kahn-blue rounded-lg flex items-center justify-center"[39m[33m>[39m
 [90m 35 |[39m               [33m<[39m[33mspan[39m className[33m=[39m[32m"text-white font-bold text-lg"[39m[33m>[39m[33mK[39m[33m<[39m[33m/[39m[33mspan[39m[33m>[39m[0m

Expected '</', got '!'

Import trace:
  ./src/components/layout/Header.tsx
  ./src/app/[locale]/layout.tsx



./src/app/[locale]/layout.tsx:4:1
Module not found: Can't resolve './globals.css'
[0m [90m 2 |[39m [36mimport[39m { getMessages } [36mfrom[39m [32m'next-intl/server'[39m
 [90m 3 |[39m [36mimport[39m { [33mInter[39m[33m,[39m [33mNoto_Sans_SC[39m } [36mfrom[39m [32m'next/font/google'[39m
[31m[1m>[22m[39m[90m 4 |[39m [36mimport[39m [32m'./globals.css'[39m
 [90m   |[39m [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
 [90m 5 |[39m [36mimport[39m { [33mHeader[39m } [36mfrom[39m [32m'@/components/layout/Header'[39m
 [90m 6 |[39m [36mimport[39m { [33mFooter[39m } [36mfrom[39m [32m'@/components/layout/Footer'[39m
 [90m 7 |[39m[0m



https://nextjs.org/docs/messages/module-not-found



./src/lib/utils.ts:1:1
Module not found: Can't resolve 'clsx'
[0m[31m[1m>[22m[39m[90m 1 |[39m [36mimport[39m { type [33mClassValue[39m[33m,[39m clsx } [36mfrom[39m [32m"clsx"[39m
 [90m   |[39m [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
 [90m 2 |[39m [36mimport[39m { twMerge } [36mfrom[39m [32m"tailwind-merge"[39m
 [90m 3 |[39m
 [90m 4 |[39m [36mexport[39m [36mfunction[39m cn([33m...[39minputs[33m:[39m [33mClassValue[39m[]) {[0m



Import trace:
  ./src/lib/utils.ts
  ./src/components/ui/button.tsx
  ./src/app/[locale]/page.tsx

https://nextjs.org/docs/messages/module-not-found



./src/lib/utils.ts:2:1
Module not found: Can't resolve 'tailwind-merge'
[0m [90m 1 |[39m [36mimport[39m { type [33mClassValue[39m[33m,[39m clsx } [36mfrom[39m [32m"clsx"[39m
[31m[1m>[22m[39m[90m 2 |[39m [36mimport[39m { twMerge } [36mfrom[39m [32m"tailwind-merge"[39m
 [90m   |[39m [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
 [90m 3 |[39m
 [90m 4 |[39m [36mexport[39m [36mfunction[39m cn([33m...[39minputs[33m:[39m [33mClassValue[39m[]) {
 [90m 5 |[39m   [36mreturn[39m twMerge(clsx(inputs))[0m



Import trace:
  ./src/lib/utils.ts
  ./src/components/ui/button.tsx
  ./src/app/[locale]/page.tsx

https://nextjs.org/docs/messages/module-not-found


 ✓ Compiled /_error in 378ms
 GET /zh 500 in 4005ms
 ✓ Compiled /favicon.ico in 118ms
 GET /favicon.ico 500 in 130ms
 ⨯ ./src/app/[locale]/layout.tsx:4:1
Module not found: Can't resolve './globals.css'
[0m [90m 2 |[39m [36mimport[39m { getMessages } [36mfrom[39m [32m'next-intl/server'[39m
 [90m 3 |[39m [36mimport[39m { [33mInter[39m[33m,[39m [33mNoto_Sans_SC[39m } [36mfrom[39m [32m'next/font/google'[39m
[31m[1m>[22m[39m[90m 4 |[39m [36mimport[39m [32m'./globals.css'[39m
 [90m   |[39m [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
 [90m 5 |[39m [36mimport[39m { [33mHeader[39m } [36mfrom[39m [32m'@/components/layout/Header'[39m
 [90m 6 |[39m [36mimport[39m { [33mFooter[39m } [36mfrom[39m [32m'@/components/layout/Footer'[39m
 [90m 7 |[39m[0m



https://nextjs.org/docs/messages/module-not-found


 ⨯ ./src/lib/utils.ts:1:1
Module not found: Can't resolve 'clsx'
[0m[31m[1m>[22m[39m[90m 1 |[39m [36mimport[39m { type [33mClassValue[39m[33m,[39m clsx } [36mfrom[39m [32m"clsx"[39m
 [90m   |[39m [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
 [90m 2 |[39m [36mimport[39m { twMerge } [36mfrom[39m [32m"tailwind-merge"[39m
 [90m 3 |[39m
 [90m 4 |[39m [36mexport[39m [36mfunction[39m cn([33m...[39minputs[33m:[39m [33mClassValue[39m[]) {[0m



Import trace:
  ./src/lib/utils.ts
  ./src/components/ui/button.tsx
  ./src/app/[locale]/page.tsx

https://nextjs.org/docs/messages/module-not-found


 ⨯ ./src/lib/utils.ts:1:1
Module not found: Can't resolve 'clsx'
[0m[31m[1m>[22m[39m[90m 1 |[39m [36mimport[39m { type [33mClassValue[39m[33m,[39m clsx } [36mfrom[39m [32m"clsx"[39m
 [90m   |[39m [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
 [90m 2 |[39m [36mimport[39m { twMerge } [36mfrom[39m [32m"tailwind-merge"[39m
 [90m 3 |[39m
 [90m 4 |[39m [36mexport[39m [36mfunction[39m cn([33m...[39minputs[33m:[39m [33mClassValue[39m[]) {[0m



Import trace:
  ./src/lib/utils.ts
  ./src/components/ui/button.tsx
  ./src/app/[locale]/page.tsx

https://nextjs.org/docs/messages/module-not-found


 ⨯ ./src/lib/utils.ts:2:1
Module not found: Can't resolve 'tailwind-merge'
[0m [90m 1 |[39m [36mimport[39m { type [33mClassValue[39m[33m,[39m clsx } [36mfrom[39m [32m"clsx"[39m
[31m[1m>[22m[39m[90m 2 |[39m [36mimport[39m { twMerge } [36mfrom[39m [32m"tailwind-merge"[39m
 [90m   |[39m [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
 [90m 3 |[39m
 [90m 4 |[39m [36mexport[39m [36mfunction[39m cn([33m...[39minputs[33m:[39m [33mClassValue[39m[]) {
 [90m 5 |[39m   [36mreturn[39m twMerge(clsx(inputs))[0m



Import trace:
  ./src/lib/utils.ts
  ./src/components/ui/button.tsx
  ./src/app/[locale]/page.tsx

https://nextjs.org/docs/messages/module-not-found


 ⨯ ./src/lib/utils.ts:2:1
Module not found: Can't resolve 'tailwind-merge'
[0m [90m 1 |[39m [36mimport[39m { type [33mClassValue[39m[33m,[39m clsx } [36mfrom[39m [32m"clsx"[39m
[31m[1m>[22m[39m[90m 2 |[39m [36mimport[39m { twMerge } [36mfrom[39m [32m"tailwind-merge"[39m
 [90m   |[39m [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
 [90m 3 |[39m
 [90m 4 |[39m [36mexport[39m [36mfunction[39m cn([33m...[39minputs[33m:[39m [33mClassValue[39m[]) {
 [90m 5 |[39m   [36mreturn[39m twMerge(clsx(inputs))[0m



Import trace:
  ./src/lib/utils.ts
  ./src/components/ui/button.tsx
  ./src/app/[locale]/page.tsx

https://nextjs.org/docs/messages/module-not-found


 ⨯ ./src/app/[locale]/layout.tsx:4:1
Module not found: Can't resolve './globals.css'
[0m [90m 2 |[39m [36mimport[39m { getMessages } [36mfrom[39m [32m'next-intl/server'[39m
 [90m 3 |[39m [36mimport[39m { [33mInter[39m[33m,[39m [33mNoto_Sans_SC[39m } [36mfrom[39m [32m'next/font/google'[39m
[31m[1m>[22m[39m[90m 4 |[39m [36mimport[39m [32m'./globals.css'[39m
 [90m   |[39m [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
 [90m 5 |[39m [36mimport[39m { [33mHeader[39m } [36mfrom[39m [32m'@/components/layout/Header'[39m
 [90m 6 |[39m [36mimport[39m { [33mFooter[39m } [36mfrom[39m [32m'@/components/layout/Footer'[39m
 [90m 7 |[39m[0m



https://nextjs.org/docs/messages/module-not-found


 ⨯ ./src/lib/utils.ts:1:1
Module not found: Can't resolve 'clsx'
[0m[31m[1m>[22m[39m[90m 1 |[39m [36mimport[39m { type [33mClassValue[39m[33m,[39m clsx } [36mfrom[39m [32m"clsx"[39m
 [90m   |[39m [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
 [90m 2 |[39m [36mimport[39m { twMerge } [36mfrom[39m [32m"tailwind-merge"[39m
 [90m 3 |[39m
 [90m 4 |[39m [36mexport[39m [36mfunction[39m cn([33m...[39minputs[33m:[39m [33mClassValue[39m[]) {[0m



Import trace:
  ./src/lib/utils.ts
  ./src/components/ui/button.tsx
  ./src/app/[locale]/page.tsx

https://nextjs.org/docs/messages/module-not-found


 ⨯ ./src/lib/utils.ts:1:1
Module not found: Can't resolve 'clsx'
[0m[31m[1m>[22m[39m[90m 1 |[39m [36mimport[39m { type [33mClassValue[39m[33m,[39m clsx } [36mfrom[39m [32m"clsx"[39m
 [90m   |[39m [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
 [90m 2 |[39m [36mimport[39m { twMerge } [36mfrom[39m [32m"tailwind-merge"[39m
 [90m 3 |[39m
 [90m 4 |[39m [36mexport[39m [36mfunction[39m cn([33m...[39minputs[33m:[39m [33mClassValue[39m[]) {[0m



Import trace:
  ./src/lib/utils.ts
  ./src/components/ui/button.tsx
  ./src/app/[locale]/page.tsx

https://nextjs.org/docs/messages/module-not-found


 ⨯ ./src/lib/utils.ts:2:1
Module not found: Can't resolve 'tailwind-merge'
[0m [90m 1 |[39m [36mimport[39m { type [33mClassValue[39m[33m,[39m clsx } [36mfrom[39m [32m"clsx"[39m
[31m[1m>[22m[39m[90m 2 |[39m [36mimport[39m { twMerge } [36mfrom[39m [32m"tailwind-merge"[39m
 [90m   |[39m [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
 [90m 3 |[39m
 [90m 4 |[39m [36mexport[39m [36mfunction[39m cn([33m...[39minputs[33m:[39m [33mClassValue[39m[]) {
 [90m 5 |[39m   [36mreturn[39m twMerge(clsx(inputs))[0m



Import trace:
  ./src/lib/utils.ts
  ./src/components/ui/button.tsx
  ./src/app/[locale]/page.tsx

https://nextjs.org/docs/messages/module-not-found


 ⨯ ./src/lib/utils.ts:2:1
Module not found: Can't resolve 'tailwind-merge'
[0m [90m 1 |[39m [36mimport[39m { type [33mClassValue[39m[33m,[39m clsx } [36mfrom[39m [32m"clsx"[39m
[31m[1m>[22m[39m[90m 2 |[39m [36mimport[39m { twMerge } [36mfrom[39m [32m"tailwind-merge"[39m
 [90m   |[39m [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
 [90m 3 |[39m
 [90m 4 |[39m [36mexport[39m [36mfunction[39m cn([33m...[39minputs[33m:[39m [33mClassValue[39m[]) {
 [90m 5 |[39m   [36mreturn[39m twMerge(clsx(inputs))[0m



Import trace:
  ./src/lib/utils.ts
  ./src/components/ui/button.tsx
  ./src/app/[locale]/page.tsx

https://nextjs.org/docs/messages/module-not-found


 ⨯ ./src/app/[locale]/layout.tsx:4:1
Module not found: Can't resolve './globals.css'
[0m [90m 2 |[39m [36mimport[39m { getMessages } [36mfrom[39m [32m'next-intl/server'[39m
 [90m 3 |[39m [36mimport[39m { [33mInter[39m[33m,[39m [33mNoto_Sans_SC[39m } [36mfrom[39m [32m'next/font/google'[39m
[31m[1m>[22m[39m[90m 4 |[39m [36mimport[39m [32m'./globals.css'[39m
 [90m   |[39m [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
 [90m 5 |[39m [36mimport[39m { [33mHeader[39m } [36mfrom[39m [32m'@/components/layout/Header'[39m
 [90m 6 |[39m [36mimport[39m { [33mFooter[39m } [36mfrom[39m [32m'@/components/layout/Footer'[39m
 [90m 7 |[39m[0m



https://nextjs.org/docs/messages/module-not-found


Error: Cannot apply unknown utility class `bg-background`. Are you using CSS modules or similar and missing `@reference`? https://tailwindcss.com/docs/functions-and-directives#reference-directive
    [2m[at onInvalidCandidate (/Users/<USER>/Documents/聂磊卡恩/kahn-building-materials/node_modules/tailwindcss/dist/lib.js:18:1312)][0m
    [2m[at ge (/Users/<USER>/Documents/聂磊卡恩/kahn-building-materials/node_modules/tailwindcss/dist/lib.js:13:29803)][0m
    [2m[at /Users/<USER>/Documents/聂磊卡恩/kahn-building-materials/node_modules/tailwindcss/dist/lib.js:18:373][0m
    [2m[at I (/Users/<USER>/Documents/聂磊卡恩/kahn-building-materials/node_modules/tailwindcss/dist/lib.js:3:1656)][0m
    [2m[at je (/Users/<USER>/Documents/聂磊卡恩/kahn-building-materials/node_modules/tailwindcss/dist/lib.js:18:172)][0m
    [2m[at bi (/Users/<USER>/Documents/聂磊卡恩/kahn-building-materials/node_modules/tailwindcss/dist/lib.js:35:780)][0m
    [2m[at process.processTicksAndRejections (node:internal/process/task_queues:105:5)][0m
    [2m[at async yi (/Users/<USER>/Documents/聂磊卡恩/kahn-building-materials/node_modules/tailwindcss/dist/lib.js:35:1123)][0m
    [2m[at async _r (/Users/<USER>/Documents/聂磊卡恩/kahn-building-materials/node_modules/@tailwindcss/node/dist/index.js:10:3384)][0m
    [2m[at async p (/Users/<USER>/Documents/聂磊卡恩/kahn-building-materials/node_modules/@tailwindcss/postcss/dist/index.js:10:4019)][0m
[?25h
