{"name": "kahn-building-materials", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@next/third-parties": "^15.4.6", "clsx": "^2.1.1", "framer-motion": "^12.23.12", "i18next": "^25.3.2", "lucide-react": "^0.536.0", "next": "15.4.6", "next-i18next": "^15.4.2", "next-intl": "^4.3.4", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.62.0", "react-i18next": "^15.6.1", "tailwind-merge": "^3.3.1", "zod": "^4.0.15"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.20", "eslint": "^9", "eslint-config-next": "15.4.6", "postcss": "^8.4.49", "tailwindcss": "^3.4.17", "typescript": "^5"}}