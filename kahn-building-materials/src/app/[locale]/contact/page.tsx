import { getTranslations } from 'next-intl/server'
import { InquiryForm } from '@/components/forms/InquiryForm'
import { Phone, Mail, MapPin } from 'lucide-react'

export default async function ContactPage({
  params
}: {
  params: Promise<{ locale: string }>
}) {
  const { locale: resolvedLocale } = await params
  const t = await getTranslations('contact')

  const contactInfo = [
    {
      icon: Phone,
      title: 'Phone',
      content: '+86-571-8278-8888',
      href: 'tel:+8657182788888'
    },
    {
      icon: Mail,
      title: 'Email',
      content: '<EMAIL>',
      href: 'mailto:<EMAIL>'
    },
    {
      icon: MapPin,
      title: 'Address',
      content: 'No. 168 Chengxin Street, Xiaoshan District, Hangzhou, China',
      href: 'https://maps.google.com/?q=No.+168+Chengxin+Street,+Xiaoshan+District,+Hangzhou,+China'
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">{t('title')}</h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Get in touch with our sales team for quotes, samples, and technical support
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-12">
          {/* Contact Form */}
          <div>
            <InquiryForm />
          </div>

          {/* Contact Information */}
          <div className="space-y-8">
            <div>
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Contact Information</h2>
              
              <div className="space-y-6">
                {contactInfo.map((item) => (
                  <div key={item.title} className="flex items-start space-x-4">
                    <div className="w-12 h-12 bg-kahn-blue/10 rounded-lg flex items-center justify-center flex-shrink-0">
                      <item.icon className="w-6 h-6 text-kahn-blue" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">{item.title}</h3>
                      <a 
                        href={item.href} 
                        className="text-gray-600 hover:text-kahn-blue transition-colors"
                      >
                        {item.content}
                      </a>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Business Hours */}
            <div className="bg-white rounded-lg p-6 shadow-md">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Business Hours</h3>
              <div className="space-y-2 text-gray-600">
                <p><strong>Monday - Friday:</strong> 8:00 AM - 6:00 PM (GMT+8)</p>
                <p><strong>Saturday:</strong> 9:00 AM - 4:00 PM (GMT+8)</p>
                <p><strong>Sunday:</strong> Closed</p>
              </div>
            </div>

            {/* Map Placeholder */}
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="aspect-video bg-gray-200 flex items-center justify-center">
                <p className="text-gray-500">Interactive Map</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}