import {getRequestConfig} from 'next-intl/server';

export const i18n = {
  locales: ['en', 'zh', 'ru'],
  defaultLocale: 'en',
  localePrefix: 'always',
} as const;

export type Locale = (typeof i18n)['locales'][number];

export default getRequestConfig(async ({locale}) => {
  // Validate locale and provide fallback
  const validLocale = i18n.locales.includes(locale as any) ? locale : i18n.defaultLocale;

  return {
    locale: validLocale as string,
    messages: (await import(`./messages/${validLocale}.json`)).default
  };
});