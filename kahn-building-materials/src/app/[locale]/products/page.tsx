import { Metadata } from 'next'
import { getTranslations } from 'next-intl/server'
import Link from 'next/link'
import { ArrowLeft, Check, Star } from 'lucide-react'
import { Button } from '@/components/ui/button'

export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string }>
}): Promise<Metadata> {
  const { locale: resolvedLocale } = await params
  
  const metadataByLocale = {
    en: {
      title: 'Wallpaper Adhesive Products | Kahn Materials',
      description: 'High-quality wallpaper adhesive powder products: starch-based, PVA-based, and pregelatinized starch. ISO9001 certified, global export.',
    },
    zh: {
      title: '墙纸胶粉产品 - 杭州卡恩新型建材有限公司',
      description: '高品质墙纸胶粉产品：改性淀粉胶粉、PVA胶粉、预糊化淀粉胶粉。ISO9001认证，全球出口。',
    },
    ru: {
      title: 'Продукция клеевой смеси для обоев | Кан Материалы',
      description: 'Высококачественная клеевая смесь для обоев: крахмальная, ПВА и предварительно желатинизированный крахмал. Сертификат ISO9001.',
    }
  }

  return {
    title: metadataByLocale[resolvedLocale as keyof typeof metadataByLocale].title,
    description: metadataByLocale[resolvedLocale as keyof typeof metadataByLocale].description,
  }
}

export default async function ProductsPage({
  params
}: {
  params: Promise<{ locale: string }>
}) {
  const { locale: resolvedLocale } = await params
  const t = await getTranslations('products')

  const products = [
    {
      id: 'starch',
      name: t('categories.starch'),
      description: t('descriptions.starch'),
      features: ['Eco-friendly', 'Easy to use', 'Strong adhesion', 'Fast drying'],
      image: '/api/placeholder/400/300',
      rating: 4.8,
      reviews: 156
    },
    {
      id: 'pva',
      name: t('categories.pva'),
      description: t('descriptions.pva'),
      features: ['Superior strength', 'Water resistant', 'Long shelf life', 'Professional grade'],
      image: '/api/placeholder/400/300',
      rating: 4.9,
      reviews: 203
    },
    {
      id: 'pregel',
      name: t('categories.pregel'),
      description: t('descriptions.pregel'),
      features: ['Instant mixing', 'No lumps', 'Consistent quality', 'Cost effective'],
      image: '/api/placeholder/400/300',
      rating: 4.7,
      reviews: 89
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center space-x-4">
            <Link href={`/${resolvedLocale}`}>
              <Button variant="ghost" size="sm">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Home
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">{t('title')}</h1>
              <p className="text-gray-600 mt-2">{t('subtitle')}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Products Grid */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid md:grid-cols-3 gap-8">
          {products.map((product) => (
            <div key={product.id} className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
              <div className="aspect-w-4 aspect-h-3">
                <img
                  src={product.image}
                  alt={product.name}
                  className="w-full h-48 object-cover"
                />
              </div>
              <div className="p-6">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="text-xl font-semibold text-gray-900">{product.name}</h3>
                  <div className="flex items-center space-x-1">
                    <Star className="w-4 h-4 text-yellow-400 fill-current" />
                    <span className="text-sm font-medium">{product.rating}</span>
                    <span className="text-sm text-gray-500">({product.reviews})</span>
                  </div>
                </div>
                <p className="text-gray-600 mb-4">{product.description}</p>
                <ul className="space-y-2 mb-6">
                  {product.features.map((feature, index) => (
                    <li key={index} className="flex items-center text-sm text-gray-600">
                      <Check className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                      {feature}
                    </li>
                  ))}
                </ul>
                <Link href={`/${resolvedLocale}/contact`}>
                  <Button className="w-full">Get Quote</Button>
                </Link>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* CTA Section */}
      <div className="bg-kahn-blue text-white py-16">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold mb-4">Need Custom Solutions?</h2>
          <p className="text-xl mb-8 text-blue-100">
            We offer customized adhesive formulations for specific applications
          </p>
          <Link href={`/${resolvedLocale}/contact`}>
            <Button variant="outline" size="lg" className="bg-white text-kahn-blue hover:bg-gray-100">
              Contact Our Experts
            </Button>
          </Link>
        </div>
      </div>
    </div>
  )
}

