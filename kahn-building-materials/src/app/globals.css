@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --kahn-blue: #1B365D;
  --kahn-blue-dark: #0F2439;
  --background: #ffffff;
  --foreground: #171717;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }
}

@layer base {
  * {
    border-color: hsl(var(--border));
  }
  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
  }
}

@layer utilities {
  .bg-kahn-blue {
    background-color: var(--kahn-blue);
  }
  
  .text-kahn-blue {
    color: var(--kahn-blue);
  }
  
  .bg-kahn-blue-dark {
    background-color: var(--kahn-blue-dark);
  }
  
  .text-kahn-blue-dark {
    color: var(--kahn-blue-dark);
  }
  
  .hover\:bg-kahn-blue\/90:hover {
    background-color: rgba(27, 54, 93, 0.9);
  }
  
  .hover\:text-kahn-blue:hover {
    color: var(--kahn-blue);
  }
  
  .hover\:bg-kahn-blue\/10:hover {
    background-color: rgba(27, 54, 93, 0.1);
  }
}
